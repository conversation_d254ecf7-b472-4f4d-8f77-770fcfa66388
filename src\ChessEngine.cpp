#include "ChessEngine.h"
#include <algorithm>
#include <random>
#include <chrono>
#include <iostream>

// Piece-square tables (values from white's perspective)
const int ChessEngine::PAWN_TABLE[8][8] = {
    {0, 0, 0, 0, 0, 0, 0, 0},
    {50, 50, 50, 50, 50, 50, 50, 50},
    {10, 10, 20, 30, 30, 20, 10, 10},
    {5, 5, 10, 25, 25, 10, 5, 5},
    {0, 0, 0, 20, 20, 0, 0, 0},
    {5, -5, -10, 0, 0, -10, -5, 5},
    {5, 10, 10, -20, -20, 10, 10, 5},
    {0, 0, 0, 0, 0, 0, 0, 0}};

const int ChessEngine::KNIGHT_TABLE[8][8] = {
    {-50, -40, -30, -30, -30, -30, -40, -50},
    {-40, -20, 0, 0, 0, 0, -20, -40},
    {-30, 0, 10, 15, 15, 10, 0, -30},
    {-30, 5, 15, 20, 20, 15, 5, -30},
    {-30, 0, 15, 20, 20, 15, 0, -30},
    {-30, 5, 10, 15, 15, 10, 5, -30},
    {-40, -20, 0, 5, 5, 0, -20, -40},
    {-50, -40, -30, -30, -30, -30, -40, -50}};

const int ChessEngine::BISHOP_TABLE[8][8] = {
    {-20, -10, -10, -10, -10, -10, -10, -20},
    {-10, 0, 0, 0, 0, 0, 0, -10},
    {-10, 0, 5, 10, 10, 5, 0, -10},
    {-10, 5, 5, 10, 10, 5, 5, -10},
    {-10, 0, 10, 10, 10, 10, 0, -10},
    {-10, 10, 10, 10, 10, 10, 10, -10},
    {-10, 5, 0, 0, 0, 0, 5, -10},
    {-20, -10, -10, -10, -10, -10, -10, -20}};

const int ChessEngine::ROOK_TABLE[8][8] = {
    {0, 0, 0, 0, 0, 0, 0, 0},
    {5, 10, 10, 10, 10, 10, 10, 5},
    {-5, 0, 0, 0, 0, 0, 0, -5},
    {-5, 0, 0, 0, 0, 0, 0, -5},
    {-5, 0, 0, 0, 0, 0, 0, -5},
    {-5, 0, 0, 0, 0, 0, 0, -5},
    {-5, 0, 0, 0, 0, 0, 0, -5},
    {0, 0, 0, 5, 5, 0, 0, 0}};

const int ChessEngine::QUEEN_TABLE[8][8] = {
    {-20, -10, -10, -5, -5, -10, -10, -20},
    {-10, 0, 0, 0, 0, 0, 0, -10},
    {-10, 0, 5, 5, 5, 5, 0, -10},
    {-5, 0, 5, 5, 5, 5, 0, -5},
    {0, 0, 5, 5, 5, 5, 0, -5},
    {-10, 5, 5, 5, 5, 5, 0, -10},
    {-10, 0, 5, 0, 0, 0, 0, -10},
    {-20, -10, -10, -5, -5, -10, -10, -20}};

const int ChessEngine::KING_MIDDLE_GAME_TABLE[8][8] = {
    {-30, -40, -40, -50, -50, -40, -40, -30},
    {-30, -40, -40, -50, -50, -40, -40, -30},
    {-30, -40, -40, -50, -50, -40, -40, -30},
    {-30, -40, -40, -50, -50, -40, -40, -30},
    {-20, -30, -30, -40, -40, -30, -30, -20},
    {-10, -20, -20, -20, -20, -20, -20, -10},
    {20, 20, 0, 0, 0, 0, 20, 20},
    {20, 30, 10, 0, 0, 10, 30, 20}};

const int ChessEngine::KING_END_GAME_TABLE[8][8] = {
    {-50, -40, -30, -20, -20, -30, -40, -50},
    {-30, -20, -10, 0, 0, -10, -20, -30},
    {-30, -10, 20, 30, 30, 20, -10, -30},
    {-30, -10, 30, 40, 40, 30, -10, -30},
    {-30, -10, 30, 40, 40, 30, -10, -30},
    {-30, -10, 20, 30, 30, 20, -10, -30},
    {-30, -30, 0, 0, 0, 0, -30, -30},
    {-50, -30, -30, -30, -30, -30, -30, -50}};

ChessEngine::ChessEngine(Color color, int depth)
    : maxDepth_(depth), engineColor_(color), maxSearchTimeMs_(5000), timeUp_(false),
      nodesSearched_(0), ttHits_(0), nullMoveCutoffs_(0), lmrReductions_(0), multiCutPrunings_(0),
      allowNullMove_(true), nullMoveReduction_(2), lmrThreshold_(3), lmrReduction_(1)
{
    transpositionTable_.reserve(TT_SIZE);

    // Initialize move ordering tables
    for (int i = 0; i < 64; ++i)
    {
        killerMoves_[i][0] = Move();
        killerMoves_[i][1] = Move();
        principalVariation_[i] = Move();

        for (int j = 0; j < 64; ++j)
        {
            historyTable_[i][j] = 0;
        }
    }
}

Move ChessEngine::getBestMove(const ChessGame &game)
{
    return getBestMoveWithTime(game, maxSearchTimeMs_);
}

Move ChessEngine::getBestMoveWithTime(const ChessGame &game, int timeMs)
{
    // First check opening book
    if (hasOpeningMove(game))
    {
        Move bookMove = getOpeningMove(game);
        if (bookMove.isValid())
        {
            std::cout << "Using opening book move.\n";
            return bookMove;
        }
    }

    maxSearchTimeMs_ = timeMs;
    searchStartTime_ = std::chrono::steady_clock::now();
    resetSearchStats();

    ChessGame gameCopy = game;
    return iterativeDeepening(gameCopy, maxDepth_);
}

Move ChessEngine::iterativeDeepening(ChessGame &game, int maxDepth)
{
    std::vector<Move> validMoves = game.getAllValidMoves();
    if (validMoves.empty())
    {
        return Move(); // No valid moves
    }

    Move bestMove = validMoves[0];
    int bestScore = std::numeric_limits<int>::min();

    // Track time for each depth
    auto searchStart = std::chrono::steady_clock::now();

    std::cout << "Starting iterative deepening search...\n";

    // Iterative deepening from depth 1 to maxDepth
    for (int depth = 1; depth <= maxDepth && !isTimeUp(); ++depth)
    {
        auto depthStart = std::chrono::steady_clock::now();

        int alpha = std::numeric_limits<int>::min();
        int beta = std::numeric_limits<int>::max();

        // Use previous iteration's best move for move ordering
        Move pvMove = (depth > 1) ? bestMove : Move();
        validMoves = orderMovesAdvanced(validMoves, game, depth, pvMove);

        Move currentBestMove = validMoves[0];
        int currentBestScore = std::numeric_limits<int>::min();
        int movesSearched = 0;

        for (const Move &move : validMoves)
        {
            if (isTimeUp())
                break;

            ChessGame tempGame = game;
            if (tempGame.makeMove(move))
            {
                int score;
                if (movesSearched == 0)
                {
                    // Full window search for first move (PV node)
                    score = principalVariationSearch(tempGame, depth - 1, alpha, beta, false);
                }
                else
                {
                    // Null window search for other moves
                    score = principalVariationSearch(tempGame, depth - 1, alpha, alpha + 1, false);
                    if (score > alpha && score < beta)
                    {
                        // Re-search with full window
                        score = principalVariationSearch(tempGame, depth - 1, score, beta, false);
                    }
                }

                if (score > currentBestScore)
                {
                    currentBestScore = score;
                    currentBestMove = move;
                    principalVariation_[depth] = move;
                }

                alpha = std::max(alpha, score);
                if (beta <= alpha)
                {
                    break; // Alpha-beta cutoff
                }

                movesSearched++;
            }
        }

        // If we completed this depth, update best move
        if (!isTimeUp())
        {
            bestMove = currentBestMove;
            bestScore = currentBestScore;

            auto depthEnd = std::chrono::steady_clock::now();
            auto depthTime = std::chrono::duration_cast<std::chrono::milliseconds>(depthEnd - depthStart);

            std::cout << "Depth " << depth << ": " << bestMove.toAlgebraic()
                      << " (score: " << bestScore << ", time: " << depthTime.count() << "ms, nodes: " << nodesSearched_ << ")\n";

            // Time management: if this depth took more than 1/3 of remaining time, stop
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(depthEnd - searchStart);
            int remainingTime = maxSearchTimeMs_ - elapsed.count();

            if (depthTime.count() * 3 > remainingTime && depth >= 4)
            {
                std::cout << "Time management: stopping search early\n";
                break;
            }
        }
        else
        {
            std::cout << "Depth " << depth << ": search interrupted by time limit\n";
            break;
        }
    }

    auto searchEnd = std::chrono::steady_clock::now();
    auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(searchEnd - searchStart);
    std::cout << "Search completed in " << totalTime.count() << "ms\n";

    return bestMove;
}

int ChessEngine::minimax(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    if (isTimeUp())
    {
        return 0; // Time's up, return neutral score
    }

    // Generate position hash for transposition table
    uint64_t hash = generateHash(game.getBoard());
    Move ttMove;
    int ttScore;

    // Probe transposition table
    if (probeTT(hash, depth, alpha, beta, ttScore, ttMove))
    {
        ttHits_++;
        return ttScore;
    }

    if (depth == 0 || game.getGameState() != GameState::PLAYING)
    {
        if (game.getGameState() != GameState::PLAYING)
        {
            // Game over - return exact evaluation
            Color perspective = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);
            int score = evaluatePosition(game.getBoard(), perspective);
            storeTT(hash, depth, score, Move(), TTEntry::EXACT);
            return score;
        }
        else
        {
            // Depth 0 - enter quiescence search
            int score = quiescenceSearch(game, alpha, beta, maximizingPlayer);
            storeTT(hash, depth, score, Move(), TTEntry::EXACT);
            return score;
        }
    }

    std::vector<Move> validMoves = game.getAllValidMoves();
    validMoves = orderMovesAdvanced(validMoves, game, depth, ttMove);

    Move bestMove;
    TTEntry::Flag flag = TTEntry::UPPER_BOUND;

    if (maximizingPlayer)
    {
        int maxEval = std::numeric_limits<int>::min();
        for (const Move &move : validMoves)
        {
            if (isTimeUp())
                break;

            ChessGame tempGame = game;
            if (tempGame.makeMove(move))
            {
                int eval = minimax(tempGame, depth - 1, alpha, beta, false);
                if (eval > maxEval)
                {
                    maxEval = eval;
                    bestMove = move;
                }
                alpha = std::max(alpha, eval);
                if (beta <= alpha)
                {
                    flag = TTEntry::LOWER_BOUND;
                    // Update killer moves and history table for cutoff move
                    updateKillerMoves(move, depth);
                    updateHistoryTable(move, depth);
                    break; // Beta cutoff
                }
            }
        }
        if (alpha > std::numeric_limits<int>::min() + 1000)
        {
            flag = TTEntry::EXACT;
        }
        storeTT(hash, depth, maxEval, bestMove, flag);
        return maxEval;
    }
    else
    {
        int minEval = std::numeric_limits<int>::max();
        for (const Move &move : validMoves)
        {
            if (isTimeUp())
                break;

            ChessGame tempGame = game;
            if (tempGame.makeMove(move))
            {
                int eval = minimax(tempGame, depth - 1, alpha, beta, true);
                if (eval < minEval)
                {
                    minEval = eval;
                    bestMove = move;
                }
                beta = std::min(beta, eval);
                if (beta <= alpha)
                {
                    flag = TTEntry::UPPER_BOUND;
                    // Update killer moves and history table for cutoff move
                    updateKillerMoves(move, depth);
                    updateHistoryTable(move, depth);
                    break; // Alpha cutoff
                }
            }
        }
        if (beta < std::numeric_limits<int>::max() - 1000)
        {
            flag = TTEntry::EXACT;
        }
        storeTT(hash, depth, minEval, bestMove, flag);
        return minEval;
    }
}

int ChessEngine::quiescenceSearch(ChessGame &game, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    if (isTimeUp())
    {
        return 0; // Time's up
    }

    // Stand pat evaluation
    Color perspective = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);
    int standPat = evaluatePosition(game.getBoard(), perspective);

    if (maximizingPlayer)
    {
        if (standPat >= beta)
        {
            return beta; // Beta cutoff
        }
        alpha = std::max(alpha, standPat);
    }
    else
    {
        if (standPat <= alpha)
        {
            return alpha; // Alpha cutoff
        }
        beta = std::min(beta, standPat);
    }

    // Generate only captures and promotions
    std::vector<Move> captures = generateCaptures(game);

    // Order captures by MVV-LVA
    std::sort(captures.begin(), captures.end(), [&game, this](const Move &a, const Move &b)
              { return getMoveScore(a, game, 0, Move()) > getMoveScore(b, game, 0, Move()); });

    for (const Move &move : captures)
    {
        if (isTimeUp())
            break;

        // Skip bad captures (negative SEE)
        if (staticExchangeEvaluation(game, move) < 0)
        {
            continue;
        }

        ChessGame tempGame = game;
        if (tempGame.makeMove(move))
        {
            int score = quiescenceSearch(tempGame, alpha, beta, !maximizingPlayer);

            if (maximizingPlayer)
            {
                if (score >= beta)
                {
                    return beta; // Beta cutoff
                }
                alpha = std::max(alpha, score);
            }
            else
            {
                if (score <= alpha)
                {
                    return alpha; // Alpha cutoff
                }
                beta = std::min(beta, score);
            }
        }
    }

    return maximizingPlayer ? alpha : beta;
}

int ChessEngine::principalVariationSearch(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    if (isTimeUp())
    {
        return 0; // Time's up, return neutral score
    }

    // Generate position hash for transposition table
    uint64_t hash = generateHash(game.getBoard());
    Move ttMove;
    int ttScore;

    // Probe transposition table
    if (probeTT(hash, depth, alpha, beta, ttScore, ttMove))
    {
        ttHits_++;
        return ttScore;
    }

    // Probe endgame tablebase
    EndgameTablebase::TablebaseEntry tbEntry;
    Color sideToMove = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);
    if (tablebase_.probe(game.getBoard(), sideToMove, tbEntry))
    {
        int tbScore = tablebase_.resultToScore(tbEntry, sideToMove);
        // Adjust score based on perspective
        if (!maximizingPlayer)
        {
            tbScore = -tbScore;
        }
        storeTT(hash, depth, tbScore, Move(), TTEntry::EXACT);
        return tbScore;
    }

    if (depth == 0 || game.getGameState() != GameState::PLAYING)
    {
        if (game.getGameState() != GameState::PLAYING)
        {
            // Game over - return exact evaluation
            Color perspective = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);
            int score = evaluatePosition(game.getBoard(), perspective);
            storeTT(hash, depth, score, Move(), TTEntry::EXACT);
            return score;
        }
        else
        {
            // Depth 0 - enter quiescence search
            int score = quiescenceSearch(game, alpha, beta, maximizingPlayer);
            storeTT(hash, depth, score, Move(), TTEntry::EXACT);
            return score;
        }
    }

    bool inCheck = game.isInCheck();

    // Null Move Pruning
    if (allowNullMove_ && !inCheck && depth >= nullMoveReduction_ + 1)
    {
        if (canDoNullMove(game, maximizingPlayer))
        {
            int nullScore = nullMoveSearch(game, depth, beta, maximizingPlayer);
            if (nullScore >= beta)
            {
                nullMoveCutoffs_++;
                return beta; // Null move cutoff
            }
        }
    }

    std::vector<Move> validMoves = game.getAllValidMoves();
    validMoves = orderMovesAdvanced(validMoves, game, depth, ttMove);

    // Static evaluation for futility pruning
    Color perspective = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);
    int staticEval = evaluatePosition(game.getBoard(), perspective);

    Move bestMove;
    TTEntry::Flag flag = TTEntry::UPPER_BOUND;
    int movesSearched = 0;
    int cutoffCount = 0; // For Multi-Cut Pruning

    if (maximizingPlayer)
    {
        int maxEval = std::numeric_limits<int>::min();
        for (const Move &move : validMoves)
        {
            if (isTimeUp())
                break;

            ChessGame tempGame = game;
            if (tempGame.makeMove(move))
            {
                int score;

                if (movesSearched == 0)
                {
                    // First move - full window search
                    score = principalVariationSearch(tempGame, depth - 1, alpha, beta, false);
                }
                else
                {
                    // Futility Pruning - skip moves that can't improve alpha
                    if (canPruneFutility(depth, alpha, staticEval, inCheck))
                    {
                        // Skip this move - it's unlikely to improve alpha
                        movesSearched++;
                        continue;
                    }

                    // Late Move Reductions
                    int reduction = 0;
                    if (shouldReduceMove(move, movesSearched, depth, inCheck))
                    {
                        reduction = getReduction(depth, movesSearched);
                        lmrReductions_++;
                    }

                    // Null window search with possible reduction
                    score = principalVariationSearch(tempGame, depth - 1 - reduction, alpha, alpha + 1, false);

                    // If move looks good, re-search with full window and depth
                    if (score > alpha)
                    {
                        if (reduction > 0)
                        {
                            // Re-search with full depth
                            score = principalVariationSearch(tempGame, depth - 1, alpha, alpha + 1, false);
                        }

                        if (score > alpha && score < beta)
                        {
                            // Re-search with full window
                            score = principalVariationSearch(tempGame, depth - 1, score, beta, false);
                        }
                    }
                }

                if (score > maxEval)
                {
                    maxEval = score;
                    bestMove = move;
                }
                alpha = std::max(alpha, score);
                if (beta <= alpha)
                {
                    flag = TTEntry::LOWER_BOUND;
                    // Update killer moves and history table for cutoff move
                    updateKillerMoves(move, depth);
                    updateHistoryTable(move, depth);
                    cutoffCount++;

                    // Multi-Cut Pruning: if we have enough cutoffs, prune remaining moves
                    if (shouldDoMultiCut(depth, cutoffCount))
                    {
                        multiCutPrunings_++;
                        break; // Multi-cut pruning
                    }

                    break; // Beta cutoff
                }

                movesSearched++;
            }
        }
        if (alpha > std::numeric_limits<int>::min() + 1000)
        {
            flag = TTEntry::EXACT;
        }
        storeTT(hash, depth, maxEval, bestMove, flag);
        return maxEval;
    }
    else
    {
        int minEval = std::numeric_limits<int>::max();
        for (const Move &move : validMoves)
        {
            if (isTimeUp())
                break;

            ChessGame tempGame = game;
            if (tempGame.makeMove(move))
            {
                int score;

                if (movesSearched == 0)
                {
                    // First move - full window search
                    score = principalVariationSearch(tempGame, depth - 1, alpha, beta, true);
                }
                else
                {
                    // Futility Pruning - skip moves that can't improve beta (for minimizing player)
                    if (canPruneFutility(depth, -beta, -staticEval, inCheck))
                    {
                        // Skip this move - it's unlikely to improve beta
                        movesSearched++;
                        continue;
                    }

                    // Late Move Reductions
                    int reduction = 0;
                    if (shouldReduceMove(move, movesSearched, depth, inCheck))
                    {
                        reduction = getReduction(depth, movesSearched);
                        lmrReductions_++;
                    }

                    // Null window search with possible reduction
                    score = principalVariationSearch(tempGame, depth - 1 - reduction, beta - 1, beta, true);

                    // If move looks good, re-search with full window and depth
                    if (score < beta)
                    {
                        if (reduction > 0)
                        {
                            // Re-search with full depth
                            score = principalVariationSearch(tempGame, depth - 1, beta - 1, beta, true);
                        }

                        if (score > alpha && score < beta)
                        {
                            // Re-search with full window
                            score = principalVariationSearch(tempGame, depth - 1, alpha, score, true);
                        }
                    }
                }

                if (score < minEval)
                {
                    minEval = score;
                    bestMove = move;
                }
                beta = std::min(beta, score);
                if (beta <= alpha)
                {
                    flag = TTEntry::UPPER_BOUND;
                    // Update killer moves and history table for cutoff move
                    updateKillerMoves(move, depth);
                    updateHistoryTable(move, depth);
                    cutoffCount++;

                    // Multi-Cut Pruning: if we have enough cutoffs, prune remaining moves
                    if (shouldDoMultiCut(depth, cutoffCount))
                    {
                        multiCutPrunings_++;
                        break; // Multi-cut pruning
                    }

                    break; // Alpha cutoff
                }

                movesSearched++;
            }
        }
        if (beta < std::numeric_limits<int>::max() - 1000)
        {
            flag = TTEntry::EXACT;
        }
        storeTT(hash, depth, minEval, bestMove, flag);
        return minEval;
    }
}

int ChessEngine::evaluatePosition(const ChessBoard &board, Color perspective) const
{
    int mgScore = 0; // Middlegame score
    int egScore = 0; // Endgame score
    bool endGame = isEndGame(board);

    // Calculate game phase for tapered evaluation
    int gamePhase = calculateGamePhase(board);

    // Material and positional evaluation
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece)
            {
                // Evaluate for both middlegame and endgame
                int mgPieceValue = evaluatePiece(piece, pos, false); // Middlegame
                int egPieceValue = evaluatePiece(piece, pos, true);  // Endgame

                if (piece->getColor() == perspective)
                {
                    mgScore += mgPieceValue;
                    egScore += egPieceValue;
                }
                else
                {
                    mgScore -= mgPieceValue;
                    egScore -= egPieceValue;
                }
            }
        }
    }

    // Advanced evaluation components (simplified for tapered eval)
    int pawnStructure = evaluatePawnStructure(board, perspective) - evaluatePawnStructure(board, oppositeColor(perspective));
    int kingSafety = evaluateKingSafety(board, perspective) - evaluateKingSafety(board, oppositeColor(perspective));
    int pieceActivity = evaluatePieceActivity(board, perspective) - evaluatePieceActivity(board, oppositeColor(perspective));
    int mobility = evaluateMobility(board, perspective) - evaluateMobility(board, oppositeColor(perspective));

    // Add to both middlegame and endgame scores (can be refined later)
    mgScore += pawnStructure + kingSafety + pieceActivity + mobility;
    egScore += pawnStructure + (kingSafety / 2) + pieceActivity + mobility; // King safety less important in endgame

    // Add bonus for checkmate
    if (board.isCheckmate(oppositeColor(perspective)))
    {
        mgScore += 100000;
        egScore += 100000;
    }
    else if (board.isCheckmate(perspective))
    {
        mgScore -= 100000;
        egScore -= 100000;
    }

    // Apply tapered evaluation
    return taperedEval(mgScore, egScore, gamePhase);
}

int ChessEngine::evaluatePiece(const Piece *piece, const Position &pos, bool isEndGame) const
{
    int value = piece->getValue();
    value += getPieceSquareValue(piece->getType(), pos, piece->getColor(), isEndGame);
    return value;
}

bool ChessEngine::isEndGame(const ChessBoard &board) const
{
    int pieceCount = 0;
    int majorPieces = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                pieceCount++;
                if (piece->getType() == PieceType::QUEEN ||
                    piece->getType() == PieceType::ROOK)
                {
                    majorPieces++;
                }
            }
        }
    }

    return pieceCount <= 12 || majorPieces <= 2;
}

int ChessEngine::getPieceSquareValue(PieceType type, const Position &pos, Color color, bool isEndGame) const
{
    int rank = pos.rank;
    int file = pos.file;

    // Flip rank for black pieces (piece-square tables are from white's perspective)
    if (color == Color::BLACK)
    {
        rank = 7 - rank;
    }

    switch (type)
    {
    case PieceType::PAWN:
        return PAWN_TABLE[rank][file];
    case PieceType::KNIGHT:
        return KNIGHT_TABLE[rank][file];
    case PieceType::BISHOP:
        return BISHOP_TABLE[rank][file];
    case PieceType::ROOK:
        return ROOK_TABLE[rank][file];
    case PieceType::QUEEN:
        return QUEEN_TABLE[rank][file];
    case PieceType::KING:
        return isEndGame ? KING_END_GAME_TABLE[rank][file] : KING_MIDDLE_GAME_TABLE[rank][file];
    default:
        return 0;
    }
}

std::vector<Move> ChessEngine::orderMoves(const std::vector<Move> &moves, const ChessGame &game) const
{
    std::vector<Move> orderedMoves = moves;

    // Simple move ordering: captures first, then other moves
    std::sort(orderedMoves.begin(), orderedMoves.end(), [&game](const Move &a, const Move &b)
              {
                  const ChessBoard &board = game.getBoard();
                  bool aIsCapture = board.getPiece(a.getTo()) != nullptr;
                  bool bIsCapture = board.getPiece(b.getTo()) != nullptr;

                  if (aIsCapture && !bIsCapture)
                      return true;
                  if (!aIsCapture && bIsCapture)
                      return false;

                  // If both are captures, prioritize higher value captures
                  if (aIsCapture && bIsCapture)
                  {
                      const Piece *aPiece = board.getPiece(a.getTo());
                      const Piece *bPiece = board.getPiece(b.getTo());
                      if (aPiece && bPiece)
                      {
                          return aPiece->getValue() > bPiece->getValue();
                      }
                  }

                  return false; // Keep original order for non-captures
              });

    return orderedMoves;
}

// Transposition Table Implementation
uint64_t ChessEngine::generateHash(const ChessBoard &board) const
{
    // Simple hash function - in production, use Zobrist hashing
    uint64_t hash = 0;
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                hash ^= (static_cast<uint64_t>(piece->getType()) + 1) *
                        (static_cast<uint64_t>(piece->getColor()) + 1) *
                        (rank * 8 + file + 1);
            }
        }
    }
    return hash;
}

void ChessEngine::storeTT(uint64_t hash, int depth, int score, const Move &bestMove, TTEntry::Flag flag)
{
    if (transpositionTable_.size() >= TT_SIZE)
    {
        // Simple replacement strategy - clear table when full
        transpositionTable_.clear();
    }

    TTEntry &entry = transpositionTable_[hash];
    entry.hash = hash;
    entry.depth = depth;
    entry.score = score;
    entry.bestMove = bestMove;
    entry.flag = flag;
}

bool ChessEngine::probeTT(uint64_t hash, int depth, int alpha, int beta, int &score, Move &bestMove)
{
    auto it = transpositionTable_.find(hash);
    if (it == transpositionTable_.end())
    {
        return false;
    }

    const TTEntry &entry = it->second;
    if (entry.depth < depth)
    {
        return false; // Not deep enough
    }

    bestMove = entry.bestMove;

    switch (entry.flag)
    {
    case TTEntry::EXACT:
        score = entry.score;
        return true;
    case TTEntry::LOWER_BOUND:
        if (entry.score >= beta)
        {
            score = entry.score;
            return true;
        }
        break;
    case TTEntry::UPPER_BOUND:
        if (entry.score <= alpha)
        {
            score = entry.score;
            return true;
        }
        break;
    }

    return false;
}

// Time Management
bool ChessEngine::isTimeUp() const
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - searchStartTime_);
    return elapsed.count() >= maxSearchTimeMs_;
}

void ChessEngine::resetSearchStats()
{
    nodesSearched_ = 0;
    ttHits_ = 0;
    nullMoveCutoffs_ = 0;
    lmrReductions_ = 0;
    multiCutPrunings_ = 0;
    timeUp_ = false;

    // Clear killer moves for new search
    for (int i = 0; i < 64; ++i)
    {
        killerMoves_[i][0] = Move();
        killerMoves_[i][1] = Move();
        principalVariation_[i] = Move();
    }
}

std::string ChessEngine::getAnalysisString() const
{
    std::string analysis = "Search Statistics:\n";
    analysis += "Nodes searched: " + std::to_string(nodesSearched_) + "\n";
    analysis += "TT hits: " + std::to_string(ttHits_) + "\n";
    analysis += "Null move cutoffs: " + std::to_string(nullMoveCutoffs_) + "\n";
    analysis += "LMR reductions: " + std::to_string(lmrReductions_) + "\n";
    analysis += "Multi-cut prunings: " + std::to_string(multiCutPrunings_) + "\n";

    if (nodesSearched_ > 0)
    {
        double ttHitRate = (double)ttHits_ / nodesSearched_ * 100.0;
        double nullMoveRate = (double)nullMoveCutoffs_ / nodesSearched_ * 100.0;
        double lmrRate = (double)lmrReductions_ / nodesSearched_ * 100.0;
        double multiCutRate = (double)multiCutPrunings_ / nodesSearched_ * 100.0;

        analysis += "TT hit rate: " + std::to_string(ttHitRate) + "%\n";
        analysis += "Null move rate: " + std::to_string(nullMoveRate) + "%\n";
        analysis += "LMR rate: " + std::to_string(lmrRate) + "%\n";
        analysis += "Multi-cut rate: " + std::to_string(multiCutRate) + "%\n";
    }
    return analysis;
}

// Opening Book Implementation
bool ChessEngine::hasOpeningMove(const ChessGame &game) const
{
    return openingBook_.hasBookMove(game);
}

Move ChessEngine::getOpeningMove(const ChessGame &game) const
{
    return openingBook_.getBookMove(game);
}

// Enhanced Evaluation Functions
int ChessEngine::evaluatePawnStructure(const ChessBoard &board, Color color) const
{
    int score = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);

            if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == color)
            {
                // Penalty for isolated pawns
                if (isPawnIsolated(board, pos))
                {
                    score -= 20;
                }

                // Penalty for doubled pawns
                if (isPawnDoubled(board, pos))
                {
                    score -= 15;
                }

                // Bonus for passed pawns
                if (isPawnPassed(board, pos))
                {
                    int rankBonus = (color == Color::WHITE) ? pos.rank : (7 - pos.rank);
                    score += 20 + rankBonus * 10; // More valuable as it advances
                }
            }
        }
    }

    return score;
}

int ChessEngine::evaluateKingSafety(const ChessBoard &board, Color color) const
{
    int score = 0;
    Position kingPos = board.findKing(color);

    if (!kingPos.isValid())
    {
        return -10000; // King not found (shouldn't happen)
    }

    // Penalty for king in center during opening/middlegame
    if (!isEndGame(board))
    {
        if (kingPos.file >= 2 && kingPos.file <= 5)
        {
            score -= 30; // King in center files
        }

        // Bonus for castling (king on g1/c1 for white, g8/c8 for black)
        int homeRank = (color == Color::WHITE) ? 0 : 7;
        if (kingPos.rank == homeRank && (kingPos.file == 2 || kingPos.file == 6))
        {
            score += 25; // Castled king
        }
    }

    // Evaluate pawn shield
    std::vector<Position> kingZone = getKingZone(kingPos);
    int pawnShield = 0;

    for (const Position &pos : kingZone)
    {
        const Piece *piece = board.getPiece(pos);
        if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == color)
        {
            pawnShield += 10;
        }
    }

    score += pawnShield;

    // Penalty for enemy pieces attacking king zone
    int attackers = 0;
    for (const Position &pos : kingZone)
    {
        attackers += countAttackers(board, pos, oppositeColor(color));
    }

    score -= attackers * 5;

    return score;
}

int ChessEngine::evaluatePieceActivity(const ChessBoard &board, Color color) const
{
    int score = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);

            if (piece && piece->getColor() == color)
            {
                switch (piece->getType())
                {
                case PieceType::KNIGHT:
                    // Knights are better in center
                    if (pos.rank >= 2 && pos.rank <= 5 && pos.file >= 2 && pos.file <= 5)
                    {
                        score += 15;
                    }
                    break;

                case PieceType::BISHOP:
                    // Bishops prefer long diagonals
                    if ((pos.rank + pos.file) % 2 == 0) // Light squares
                    {
                        if (pos.rank == pos.file || pos.rank + pos.file == 7)
                        {
                            score += 10; // On main diagonal
                        }
                    }
                    break;

                case PieceType::ROOK:
                {
                    // Rooks prefer open files and 7th rank
                    bool openFile = true;
                    for (int r = 0; r < 8; ++r)
                    {
                        const Piece *p = board.getPiece(Position(r, pos.file));
                        if (p && p->getType() == PieceType::PAWN)
                        {
                            openFile = false;
                            break;
                        }
                    }
                    if (openFile)
                    {
                        score += 20;
                    }

                    // 7th rank bonus
                    int seventhRank = (color == Color::WHITE) ? 6 : 1;
                    if (pos.rank == seventhRank)
                    {
                        score += 15;
                    }
                    break;
                }

                default:
                    break;
                }
            }
        }
    }

    return score;
}

int ChessEngine::evaluateMobility(const ChessBoard &board, Color color) const
{
    int score = 0;

    // Count total mobility for all pieces
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);

            if (piece && piece->getColor() == color)
            {
                std::vector<Position> moves = piece->getPossibleMoves(pos, board);
                int mobility = moves.size();

                switch (piece->getType())
                {
                case PieceType::QUEEN:
                    score += mobility * 2;
                    break;
                case PieceType::ROOK:
                    score += mobility * 2;
                    break;
                case PieceType::BISHOP:
                    score += mobility * 3;
                    break;
                case PieceType::KNIGHT:
                    score += mobility * 4;
                    break;
                default:
                    score += mobility;
                    break;
                }
            }
        }
    }

    return score;
}

// Evaluation Helper Functions
bool ChessEngine::isPawnIsolated(const ChessBoard &board, const Position &pawnPos) const
{
    const Piece *pawn = board.getPiece(pawnPos);
    if (!pawn || pawn->getType() != PieceType::PAWN)
    {
        return false;
    }

    Color pawnColor = pawn->getColor();

    // Check adjacent files for friendly pawns
    for (int file = pawnPos.file - 1; file <= pawnPos.file + 1; file += 2)
    {
        if (file >= 0 && file < 8 && file != pawnPos.file)
        {
            for (int rank = 0; rank < 8; ++rank)
            {
                const Piece *piece = board.getPiece(Position(rank, file));
                if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == pawnColor)
                {
                    return false; // Found friendly pawn on adjacent file
                }
            }
        }
    }

    return true; // No friendly pawns on adjacent files
}

bool ChessEngine::isPawnDoubled(const ChessBoard &board, const Position &pawnPos) const
{
    const Piece *pawn = board.getPiece(pawnPos);
    if (!pawn || pawn->getType() != PieceType::PAWN)
    {
        return false;
    }

    Color pawnColor = pawn->getColor();

    // Check same file for other friendly pawns
    for (int rank = 0; rank < 8; ++rank)
    {
        if (rank != pawnPos.rank)
        {
            const Piece *piece = board.getPiece(Position(rank, pawnPos.file));
            if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == pawnColor)
            {
                return true; // Found another friendly pawn on same file
            }
        }
    }

    return false;
}

bool ChessEngine::isPawnPassed(const ChessBoard &board, const Position &pawnPos) const
{
    const Piece *pawn = board.getPiece(pawnPos);
    if (!pawn || pawn->getType() != PieceType::PAWN)
    {
        return false;
    }

    Color pawnColor = pawn->getColor();
    Color enemyColor = oppositeColor(pawnColor);

    int direction = (pawnColor == Color::WHITE) ? 1 : -1;
    int startRank = pawnPos.rank + direction;
    int endRank = (pawnColor == Color::WHITE) ? 8 : -1;

    // Check files: same file and adjacent files
    for (int file = pawnPos.file - 1; file <= pawnPos.file + 1; ++file)
    {
        if (file >= 0 && file < 8)
        {
            for (int rank = startRank; rank != endRank; rank += direction)
            {
                if (rank >= 0 && rank < 8)
                {
                    const Piece *piece = board.getPiece(Position(rank, file));
                    if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == enemyColor)
                    {
                        return false; // Enemy pawn blocks or can capture
                    }
                }
            }
        }
    }

    return true; // No enemy pawns can stop this pawn
}

int ChessEngine::countAttackers(const ChessBoard &board, const Position &pos, Color attackingColor) const
{
    int count = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position piecePos(rank, file);
            const Piece *piece = board.getPiece(piecePos);

            if (piece && piece->getColor() == attackingColor)
            {
                if (piece->canMoveTo(piecePos, pos, board))
                {
                    count++;
                }
            }
        }
    }

    return count;
}

std::vector<Position> ChessEngine::getKingZone(const Position &kingPos) const
{
    std::vector<Position> zone;

    // 3x3 area around king
    for (int rankOffset = -1; rankOffset <= 1; ++rankOffset)
    {
        for (int fileOffset = -1; fileOffset <= 1; ++fileOffset)
        {
            Position pos(kingPos.rank + rankOffset, kingPos.file + fileOffset);
            if (pos.isValid())
            {
                zone.push_back(pos);
            }
        }
    }

    return zone;
}

// Advanced Move Ordering
std::vector<Move> ChessEngine::orderMovesAdvanced(const std::vector<Move> &moves, const ChessGame &game, int depth, const Move &ttMove) const
{
    std::vector<std::pair<Move, int>> scoredMoves;

    for (const Move &move : moves)
    {
        int score = getMoveScore(move, game, depth, ttMove);
        scoredMoves.push_back({move, score});
    }

    // Sort by score (highest first)
    std::sort(scoredMoves.begin(), scoredMoves.end(),
              [](const std::pair<Move, int> &a, const std::pair<Move, int> &b)
              {
                  return a.second > b.second;
              });

    std::vector<Move> orderedMoves;
    for (const auto &pair : scoredMoves)
    {
        orderedMoves.push_back(pair.first);
    }

    return orderedMoves;
}

int ChessEngine::getMoveScore(const Move &move, const ChessGame &game, int depth, const Move &ttMove) const
{
    int score = 0;

    // 1. Transposition table move gets highest priority
    if (move == ttMove)
    {
        return 10000;
    }

    const ChessBoard &board = game.getBoard();

    // 2. Captures (MVV-LVA: Most Valuable Victim - Least Valuable Attacker)
    const Piece *capturedPiece = board.getPiece(move.getTo());
    if (capturedPiece)
    {
        const Piece *attackingPiece = board.getPiece(move.getFrom());
        if (attackingPiece)
        {
            int victimValue = PIECE_VALUES[static_cast<int>(capturedPiece->getType())];
            int attackerValue = PIECE_VALUES[static_cast<int>(attackingPiece->getType())];
            score += 8000 + victimValue - attackerValue;
        }
    }

    // 3. Killer moves
    if (depth >= 0 && depth < 64)
    {
        if (move == killerMoves_[depth][0])
        {
            score += 7000;
        }
        else if (move == killerMoves_[depth][1])
        {
            score += 6000;
        }
    }

    // 4. History heuristic
    int fromSquare = move.getFrom().rank * 8 + move.getFrom().file;
    int toSquare = move.getTo().rank * 8 + move.getTo().file;
    if (fromSquare >= 0 && fromSquare < 64 && toSquare >= 0 && toSquare < 64)
    {
        score += historyTable_[fromSquare][toSquare];
    }

    // 5. Promotions
    if (move.getType() == MoveType::PAWN_PROMOTION)
    {
        score += 5000 + PIECE_VALUES[static_cast<int>(move.getPromotionPiece())];
    }

    // 6. Castling
    if (move.getType() == MoveType::CASTLE_KINGSIDE || move.getType() == MoveType::CASTLE_QUEENSIDE)
    {
        score += 4000;
    }

    // 7. Center control
    const Piece *movingPiece = board.getPiece(move.getFrom());
    if (movingPiece)
    {
        Position to = move.getTo();
        if (to.rank >= 3 && to.rank <= 4 && to.file >= 3 && to.file <= 4)
        {
            score += 100; // Center squares
        }
        else if (to.rank >= 2 && to.rank <= 5 && to.file >= 2 && to.file <= 5)
        {
            score += 50; // Extended center
        }
    }

    return score;
}

void ChessEngine::updateKillerMoves(const Move &move, int depth)
{
    if (depth >= 0 && depth < 64)
    {
        // Don't store captures as killer moves
        // (they're already well-ordered by MVV-LVA)
        if (move.isCapture())
        {
            return;
        }

        // If this move is not already the first killer move
        if (!(move == killerMoves_[depth][0]))
        {
            // Shift moves: second becomes first, new move becomes second
            killerMoves_[depth][1] = killerMoves_[depth][0];
            killerMoves_[depth][0] = move;
        }
    }
}

void ChessEngine::updateHistoryTable(const Move &move, int depth)
{
    int fromSquare = move.getFrom().rank * 8 + move.getFrom().file;
    int toSquare = move.getTo().rank * 8 + move.getTo().file;

    if (fromSquare >= 0 && fromSquare < 64 && toSquare >= 0 && toSquare < 64)
    {
        // Increase history score based on depth (deeper searches are more valuable)
        historyTable_[fromSquare][toSquare] += depth * depth;

        // Prevent overflow
        if (historyTable_[fromSquare][toSquare] > 10000)
        {
            // Age all history scores
            for (int i = 0; i < 64; ++i)
            {
                for (int j = 0; j < 64; ++j)
                {
                    historyTable_[i][j] /= 2;
                }
            }
        }
    }
}

// Quiescence Search Helper Functions
std::vector<Move> ChessEngine::generateCaptures(const ChessGame &game) const
{
    std::vector<Move> captures;
    std::vector<Move> allMoves = game.getAllValidMoves();

    const ChessBoard &board = game.getBoard();

    for (const Move &move : allMoves)
    {
        // Include captures
        if (board.getPiece(move.getTo()) != nullptr)
        {
            captures.push_back(move);
        }
        // Include pawn promotions (even if not captures)
        else if (move.getType() == MoveType::PAWN_PROMOTION)
        {
            captures.push_back(move);
        }
        // Include en passant
        else if (move.getType() == MoveType::EN_PASSANT)
        {
            captures.push_back(move);
        }
    }

    return captures;
}

bool ChessEngine::isQuiet(const Move &move, const ChessGame &game) const
{
    const ChessBoard &board = game.getBoard();

    // Not quiet if it's a capture
    if (board.getPiece(move.getTo()) != nullptr)
    {
        return false;
    }

    // Not quiet if it's a promotion
    if (move.getType() == MoveType::PAWN_PROMOTION)
    {
        return false;
    }

    // Not quiet if it's en passant
    if (move.getType() == MoveType::EN_PASSANT)
    {
        return false;
    }

    // Not quiet if it gives check (simplified check)
    ChessGame tempGame = game;
    if (tempGame.makeMove(move))
    {
        if (tempGame.isInCheck())
        {
            return false;
        }
    }

    return true; // It's a quiet move
}

int ChessEngine::staticExchangeEvaluation(const ChessGame &game, const Move &move) const
{
    const ChessBoard &board = game.getBoard();
    Position target = move.getTo();

    const Piece *attacker = board.getPiece(move.getFrom());
    const Piece *victim = board.getPiece(target);

    if (!attacker)
    {
        return 0;
    }

    // Get all attackers to the target square
    std::vector<std::pair<int, Color>> attackers; // value, color

    // Find all pieces that can attack the target square
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece && pos != move.getFrom()) // Exclude the moving piece initially
            {
                std::vector<Position> moves = piece->getPossibleMoves(pos, board);
                if (std::find(moves.begin(), moves.end(), target) != moves.end())
                {
                    attackers.push_back({PIECE_VALUES[static_cast<int>(piece->getType())], piece->getColor()});
                }
            }
        }
    }

    // Add the initial attacker
    attackers.push_back({PIECE_VALUES[static_cast<int>(attacker->getType())], attacker->getColor()});

    // Sort attackers by value (cheapest first)
    std::sort(attackers.begin(), attackers.end());

    // Calculate initial gain
    int gain = 0;
    if (victim)
    {
        gain = PIECE_VALUES[static_cast<int>(victim->getType())];
    }

    // Handle special moves
    if (move.getType() == MoveType::EN_PASSANT)
    {
        gain = PIECE_VALUES[static_cast<int>(PieceType::PAWN)];
    }
    else if (move.getType() == MoveType::PAWN_PROMOTION)
    {
        gain += PIECE_VALUES[static_cast<int>(move.getPromotionPiece())] -
                PIECE_VALUES[static_cast<int>(PieceType::PAWN)];
    }

    // Simulate the exchange sequence
    std::vector<int> gains;
    gains.push_back(gain);

    Color currentSide = attacker->getColor();
    int currentGain = gain;

    // Process attackers alternately
    for (size_t i = 0; i < attackers.size(); ++i)
    {
        Color attackerColor = attackers[i].second;
        int attackerValue = attackers[i].first;

        if (attackerColor != currentSide)
        {
            // This attacker can recapture
            currentGain = gains.back() - attackerValue;
            gains.push_back(currentGain);
            currentSide = oppositeColor(currentSide);
        }
    }

    // Minimax backwards through the gain list
    for (int i = gains.size() - 2; i >= 0; --i)
    {
        gains[i] = std::max(gains[i], -gains[i + 1]);
    }

    return gains.empty() ? 0 : gains[0];
}

// Advanced Search Techniques Implementation
bool ChessEngine::canDoNullMove(const ChessGame &game, bool maximizingPlayer) const
{
    const ChessBoard &board = game.getBoard();
    Color currentColor = maximizingPlayer ? engineColor_ : oppositeColor(engineColor_);

    // Don't do null move if we have only king and pawns (zugzwang risk)
    int pieceCount = 0;
    bool hasNonPawnPieces = false;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece && piece->getColor() == currentColor)
            {
                pieceCount++;
                if (piece->getType() != PieceType::PAWN && piece->getType() != PieceType::KING)
                {
                    hasNonPawnPieces = true;
                }
            }
        }
    }

    // Only allow null move if we have non-pawn pieces (avoid zugzwang)
    return hasNonPawnPieces && pieceCount > 3;
}

int ChessEngine::nullMoveSearch(ChessGame &game, int depth, int beta, bool maximizingPlayer)
{
    // Make null move (switch sides without moving)
    ChessGame nullGame = game;
    // In a real implementation, we'd have a makeNullMove() method
    // For now, we simulate by switching the current player

    // Search with reduced depth
    int nullScore = principalVariationSearch(nullGame, depth - nullMoveReduction_ - 1, beta - 1, beta, !maximizingPlayer);

    return nullScore;
}

bool ChessEngine::shouldReduceMove(const Move &move, int moveIndex, int depth, bool inCheck) const
{
    // Don't reduce if in check
    if (inCheck)
    {
        return false;
    }

    // Don't reduce captures, promotions, or killer moves
    if (move.isCapture() || move.getType() == MoveType::PAWN_PROMOTION)
    {
        return false;
    }

    // Don't reduce killer moves
    if (depth >= 0 && depth < 64)
    {
        if (move == killerMoves_[depth][0] || move == killerMoves_[depth][1])
        {
            return false;
        }
    }

    // Only reduce moves after the first few
    return moveIndex >= lmrThreshold_ && depth >= 3;
}

int ChessEngine::getReduction(int depth, int moveIndex) const
{
    // Simple reduction formula
    // More sophisticated engines use logarithmic formulas
    if (depth >= 6 && moveIndex >= 6)
    {
        return 2; // Reduce by 2 for very late moves in deep searches
    }
    else if (depth >= 3 && moveIndex >= lmrThreshold_)
    {
        return 1; // Standard reduction
    }

    return 0; // No reduction
}

// Multi-Cut Pruning Implementation
bool ChessEngine::shouldDoMultiCut(int depth, int cutoffCount) const
{
    // Only apply multi-cut at sufficient depth and with enough cutoffs
    return depth >= MULTI_CUT_DEPTH && cutoffCount >= MULTI_CUT_THRESHOLD;
}

// Futility Pruning Implementation
bool ChessEngine::canPruneFutility(int depth, int alpha, int staticEval, bool inCheck) const
{
    // Don't prune if in check or at too deep levels
    if (inCheck || depth > FUTILITY_DEPTH)
    {
        return false;
    }

    // Calculate futility margin based on depth
    int margin = getFutilityMargin(depth);

    // Prune if static evaluation + margin is still below alpha
    return staticEval + margin <= alpha;
}

int ChessEngine::getFutilityMargin(int depth) const
{
    // Increasing margin for deeper levels
    return FUTILITY_MARGIN_BASE * depth;
}

// Tapered Evaluation Implementation
int ChessEngine::calculateGamePhase(const ChessBoard &board) const
{
    int phase = TOTAL_PHASE;

    // Subtract phase values for missing pieces
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                switch (piece->getType())
                {
                case PieceType::PAWN:
                    phase -= PAWN_PHASE;
                    break;
                case PieceType::KNIGHT:
                    phase -= KNIGHT_PHASE;
                    break;
                case PieceType::BISHOP:
                    phase -= BISHOP_PHASE;
                    break;
                case PieceType::ROOK:
                    phase -= ROOK_PHASE;
                    break;
                case PieceType::QUEEN:
                    phase -= QUEEN_PHASE;
                    break;
                case PieceType::KING:
                    // King doesn't affect phase
                    break;
                }
            }
        }
    }

    // Ensure phase is within bounds
    return std::max(0, std::min(TOTAL_PHASE, phase));
}

int ChessEngine::taperedEval(int mgScore, int egScore, int phase) const
{
    // Linear interpolation between middlegame and endgame scores
    return (mgScore * phase + egScore * (TOTAL_PHASE - phase)) / TOTAL_PHASE;
}
